#!/usr/bin/env python3
"""
Ray Head Node Startup Script for Distributed Timbre Transfer

This script starts the Ray head node that will coordinate the distributed cluster.
Worker nodes will connect to this head node.

Usage:
    python start_ray_head.py --port 10001 --dashboard-port 8265
    python start_ray_head.py --head-ip ************* --port 10001
"""

import os
import sys
import argparse
import logging
import torch
import ray
from ray import serve
import time
import subprocess

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_gpu_availability():
    """Check and display GPU information"""
    if torch.cuda.is_available():
        num_gpus = torch.cuda.device_count()
        logger.info(f"🖥️ Head node found {num_gpus} GPU(s) available:")
        
        for i in range(num_gpus):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_props = torch.cuda.get_device_properties(i)
            gpu_memory = gpu_props.total_memory / 1e9
            logger.info(f"  GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
            
        return num_gpus
    else:
        logger.warning("⚠️ No GPUs available on head node")
        return 0


def start_ray_head(head_ip="0.0.0.0", port=10001, dashboard_port=8265, num_gpus=3):
    """Start Ray head node using ray start --head command"""
    import subprocess

    if ray.is_initialized():
        logger.info("ℹ️ Ray already initialized - shutting down first")
        ray.shutdown()

    available_gpus = check_gpu_availability()

    if num_gpus > available_gpus:
        logger.warning(f"⚠️ Requested {num_gpus} GPUs but only {available_gpus} available")
        num_gpus = available_gpus

    logger.info(f"🚀 Starting Ray head node...")
    logger.info(f"   Head IP: {head_ip}")
    logger.info(f"   Port: {port}")
    logger.info(f"   Dashboard: http://{head_ip}:{dashboard_port}")
    logger.info(f"   GPUs: {num_gpus}")

    # Build ray start --head command
    # Use different ports to avoid conflicts with worker port range (10002-19999)
    gcs_port = port  # 10001 for GCS
    client_port = 20001  # Use port outside worker range

    ray_cmd = [
        "ray", "start", "--head",
        f"--port={gcs_port}",
        f"--ray-client-server-port={client_port}",
        f"--node-ip-address={head_ip}",
        f"--dashboard-host={head_ip}",
        f"--dashboard-port={dashboard_port}",
        f"--num-gpus={num_gpus}",
        f"--num-cpus={os.cpu_count()}",
        "--include-dashboard=true",
        "--disable-usage-stats"
    ]

    logger.info(f"🔧 Executing: {' '.join(ray_cmd)}")

    try:
        # Start Ray head node using subprocess
        result = subprocess.run(
            ray_cmd,
            capture_output=True,
            text=True,
            timeout=60
        )

        if result.returncode == 0:
            logger.info("✅ Ray head node started successfully")
            logger.info(f"📝 Ray start output: {result.stdout.strip()}")
        else:
            logger.error(f"❌ Ray start failed with return code {result.returncode}")
            logger.error(f"📝 Error output: {result.stderr.strip()}")
            return False

    except subprocess.TimeoutExpired:
        logger.error("❌ Ray start command timed out after 60 seconds")
        return False
    except Exception as e:
        logger.error(f"❌ Failed to start Ray head node: {e}")
        return False

    # Wait a moment for Ray to fully initialize
    time.sleep(3)

    # Connect to the started Ray head node
    try:
        logger.info(f"🔗 Connecting to Ray head node at {head_ip}:{gcs_port}...")
        ray.init(
            address=f"{head_ip}:{gcs_port}",
            ignore_reinit_error=True,
            log_to_driver=True
        )

        logger.info("✅ Connected to Ray head node successfully")
        logger.info(f"📊 Cluster resources: {ray.cluster_resources()}")

    except Exception as e:
        logger.error(f"❌ Failed to connect to Ray head node: {e}")
        return False

    # Print connection info for worker nodes
    logger.info(f"🌐 Ray Head Node Address: {head_ip}:{gcs_port} (static port)")
    logger.info("🔗 Worker nodes can connect using:")
    logger.info(f"   ray start --address='{head_ip}:{gcs_port}' --num-gpus=3")
    logger.info("   Or using the Python script:")
    logger.info(f"   python3 start_ray_worker.py --head-ip {head_ip} --head-port {gcs_port} --num-gpus 3")

    return True


def deploy_timbre_service():
    """Deploy the timbre transfer service to the cluster"""
    logger.info("🚀 Deploying timbre transfer service to Ray cluster...")
    
    try:
        # Import and deploy the service
        from ray_serve_timbre_only import app

        # Deploy the timbre transfer service
        serve.start(detached=True, http_options={"host": "0.0.0.0", "port": 8011})

        # Create a Ray Serve deployment for the FastAPI app
        @serve.deployment(route_prefix="/")
        @serve.ingress(app)
        class FastAPIDeployment:
            pass

        # Deploy the FastAPI app
        serve.run(FastAPIDeployment.bind(), name="timbre-transfer")
        
        logger.info("✅ Timbre transfer service deployed successfully")
        logger.info("🌐 Service available at: http://0.0.0.0:8011/timbre-transfer/")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to deploy timbre service: {e}")
        return False


def monitor_cluster():
    """Monitor the Ray cluster and wait for worker nodes"""
    logger.info("👀 Monitoring cluster for worker nodes...")
    logger.info("   Press Ctrl+C to stop the head node")
    
    try:
        while True:
            resources = ray.cluster_resources()
            nodes = ray.nodes()
            
            total_gpus = resources.get('GPU', 0)
            total_cpus = resources.get('CPU', 0)
            alive_nodes = len([n for n in nodes if n['Alive']])
            
            logger.info(f"📊 Cluster status: {alive_nodes} nodes, {total_gpus} GPUs, {total_cpus} CPUs")
            
            # Show detailed node information
            for i, node in enumerate(nodes):
                if node['Alive']:
                    node_ip = node['NodeManagerAddress']
                    node_resources = node['Resources']
                    node_gpus = node_resources.get('GPU', 0)
                    node_cpus = node_resources.get('CPU', 0)
                    logger.info(f"  Node {i+1}: {node_ip} - {node_gpus} GPUs, {node_cpus} CPUs")
            
            time.sleep(30)  # Check every 30 seconds
            
    except KeyboardInterrupt:
        logger.info("🛑 Cluster monitoring interrupted by user")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Start Ray Head Node for Distributed Timbre Transfer",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Start head node with default settings
  python start_ray_head.py
  
  # Start head node on specific IP and port
  python start_ray_head.py --head-ip ************* --port 10001
  
  # Start head node with custom dashboard port
  python start_ray_head.py --dashboard-port 8266
  
  # Start head node with specific number of GPUs
  python start_ray_head.py --num-gpus 3
        """
    )
    
    parser.add_argument(
        "--head-ip",
        type=str,
        default="0.0.0.0",
        help="IP address for the head node (default: 0.0.0.0)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=10001,
        help="Port for Ray client connections (default: 10001)"
    )
    
    parser.add_argument(
        "--dashboard-port",
        type=int,
        default=8265,
        help="Port for Ray dashboard (default: 8265)"
    )
    
    parser.add_argument(
        "--num-gpus",
        type=int,
        default=3,
        help="Number of GPUs to use on head node (default: 3)"
    )
    
    parser.add_argument(
        "--no-deploy",
        action="store_true",
        help="Don't deploy the timbre service automatically"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging"
    )
    
    args = parser.parse_args()
    
    # Set debug logging if requested
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("🐛 Debug logging enabled")
    
    # Validate arguments
    if args.port < 1 or args.port > 65535:
        logger.error("❌ Port must be between 1 and 65535")
        sys.exit(1)
    
    if args.dashboard_port < 1 or args.dashboard_port > 65535:
        logger.error("❌ Dashboard port must be between 1 and 65535")
        sys.exit(1)
    
    if args.num_gpus < 0:
        logger.error("❌ Number of GPUs cannot be negative")
        sys.exit(1)
    
    try:
        # Start Ray head node
        logger.info("🎵 Starting Ray Head Node for Distributed Timbre Transfer...")
        
        if not start_ray_head(
            head_ip=args.head_ip,
            port=args.port,
            dashboard_port=args.dashboard_port,
            num_gpus=args.num_gpus
        ):
            logger.error("❌ Failed to start Ray head node")
            sys.exit(1)
        
        # Deploy timbre service if requested
        if not args.no_deploy:
            if not deploy_timbre_service():
                logger.error("❌ Failed to deploy timbre service")
                sys.exit(1)
        
        # Monitor the cluster
        monitor_cluster()
        
    except KeyboardInterrupt:
        logger.info("🛑 Head node interrupted by user")
    except Exception as e:
        logger.error(f"❌ Head node failed: {e}")
        sys.exit(1)
    finally:
        # Cleanup
        logger.info("🧹 Cleaning up...")
        if ray.is_initialized():
            serve.shutdown()
            ray.shutdown()
        logger.info("✅ Cleanup completed")


if __name__ == "__main__":
    main()
